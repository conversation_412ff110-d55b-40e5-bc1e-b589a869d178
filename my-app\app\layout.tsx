import type { <PERSON>ada<PERSON> } from "next";
import {Inter} from "next/font/google";
import "./globals.css";
const inter = Inter({subsets:["latin"], weight: ["400", "500", "600", "700", "800", "900"]});

export const metadata: Metadata = {
  title: "Portfolio-Website",
  description: "A portfolio website for a software engineer",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
